import { formatEther, formatUnits, parseUnits } from "viem";
import {
  hyperPublicProvider,
  somniaPublicProvider,
  getNetworkConfig,
  getNetworkDecimals,
} from "@/app/providers/networkChains";
import { getCoinBalanceOnchain, getCoinDecimalsOnchain } from "./suiClient";
import { NETWORKS } from "./contants";
import { convertMistToDec, isNativeToken } from "./helper";

// ERC-20 ABI for balance and decimals
const ERC20_ABI = [
  {
    constant: true,
    inputs: [{ name: "_owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "balance", type: "uint256" }],
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    type: "function",
  },
] as const;

/**
 * Get the appropriate public provider for EVM networks
 */
const getEvmProvider = (network: string) => {
  switch (network) {
    case NETWORKS.HYPEREVM:
      return hyperPublicProvider;
    case NETWORKS.SOMNIA:
      return somniaPublicProvider;
    default:
      throw new Error(`Unsupported EVM network: ${network}`);
  }
};

export const getEvmNativeBalance = async (
  walletAddress: string,
  network: string
): Promise<string> => {
  try {
    const provider = getEvmProvider(network);
    const balance = await provider.getBalance({
      address: walletAddress as `0x${string}`,
    });
    return formatEther(balance);
  } catch (error) {
    console.error(`getEvmNativeBalance error for ${network}:`, error);
    throw error;
  }
};

export const getEvmTokenBalance = async (
  walletAddress: string,
  tokenAddress: string,
  network: string
): Promise<string> => {
  try {
    const provider = getEvmProvider(network);
    const balance = await provider.readContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: "balanceOf",
      args: [walletAddress as `0x${string}`],
    });

    const decimals = await getEvmTokenDecimals(tokenAddress, network);
    return formatUnits(balance as bigint, decimals);
  } catch (error) {
    console.error(`getEvmTokenBalance error for ${network}:`, error);
    throw error;
  }
};

export const getEvmTokenDecimals = async (
  tokenAddress: string,
  network: string
): Promise<number> => {
  try {
    if (isNativeToken(tokenAddress, network)) {
      return getNetworkDecimals(network);
    }

    const provider = getEvmProvider(network);
    const decimals = await provider.readContract({
      address: tokenAddress as `0x${string}`,
      abi: ERC20_ABI,
      functionName: "decimals",
    });

    return Number(decimals);
  } catch (error) {
    console.error(`getEvmTokenDecimals error for ${network}:`, error);
    return getNetworkDecimals(network);
  }
};

/**
 * Network-aware function to get coin balance
 */
export const getCoinBalanceByNetwork = async (
  walletAddress: string,
  tokenAddress: string,
  network: string
): Promise<string> => {
  const networkConfig = getNetworkConfig(network) as {
    chainType: "sui" | "ethereum";
  } | null;

  if (!networkConfig || typeof networkConfig.chainType !== "string") {
    throw new Error(
      `Invalid or unsupported network configuration for: ${network}`
    );
  }

  if (!networkConfig) {
    throw new Error(`Unsupported network: ${network}`);
  }

  if (networkConfig.chainType === "sui") {
    return await getCoinBalanceOnchain(walletAddress, tokenAddress);
  } else if (networkConfig.chainType === "ethereum") {
    if (isNativeToken(tokenAddress, network)) {
      const balance = await getEvmNativeBalance(walletAddress, network);
      const decimals = getNetworkDecimals(network);
      return parseUnits(balance, decimals).toString();
    } else {
      const balance = await getEvmTokenBalance(
        walletAddress,
        tokenAddress,
        network
      );
      const decimals = await getEvmTokenDecimals(tokenAddress, network);
      return parseUnits(balance, decimals).toString();
    }
  } else {
    throw new Error(`Unsupported chain type: ${networkConfig?.chainType}`);
  }
};

/**
 * Network-aware function to get coin decimals
 */
export const getCoinDecimalsByNetwork = async (
  tokenAddress: string,
  network: string
): Promise<number> => {
  const networkConfig = getNetworkConfig(network);

  if (!networkConfig) {
    throw new Error(`Unsupported network: ${network}`);
  }

  if (networkConfig.chainType === "sui") {
    // Use Sui client for Sui network
    return await getCoinDecimalsOnchain(tokenAddress);
  } else if (networkConfig.chainType === "ethereum") {
    // Use EVM providers for Ethereum-based networks
    return await getEvmTokenDecimals(tokenAddress, network);
  } else {
    throw new Error(`Unsupported chain type: ${networkConfig.chainType}`);
  }
};

/**
 * Network-aware function to convert raw balance to decimal format
 */
export const convertBalanceToDecimal = (
  rawBalance: string,
  decimals: number,
  network: string
): string => {
  return convertMistToDec(rawBalance, decimals);
};

/**
 * Network-aware function to get formatted balance
 */
export const getFormattedBalance = async (
  walletAddress: string,
  tokenAddress: string,
  network: string
): Promise<string> => {
  const rawBalance = await getCoinBalanceByNetwork(
    walletAddress,
    tokenAddress,
    network
  );
  const decimals = await getCoinDecimalsByNetwork(tokenAddress, network);
  return convertBalanceToDecimal(rawBalance, decimals, network);
};
