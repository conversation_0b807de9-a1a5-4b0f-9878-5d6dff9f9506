import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useMemo } from "react";
import { TWallet } from "@/types";
import Storage from "@/libs/storage";

export const useRaidenxWallet = () => {
  const storageWalletAddresses = Storage.getWalletAddresses();
  const wallets = useSelector((state: RootState) => state.user.wallets);

  const activeWallets = useMemo(() => {
    if (!wallets?.length) return [];

    if (storageWalletAddresses?.length) {
      const filteredWallets = wallets.filter((item) =>
        storageWalletAddresses.includes(item.address)
      );
      return filteredWallets.length ? filteredWallets : [wallets[0]];
    }

    return [wallets[0]];
  }, [wallets, storageWalletAddresses]) as TWallet[];

  const activeWalletAddresses = useMemo(
    () => activeWallets.map((wallet) => wallet?.address),
    [activeWallets]
  );

  const walletAddresses = useMemo(
    () => wallets.map((wallet) => wallet?.address),
    [wallets]
  );

  const totalSuiBalance = useMemo(
    () => wallets.reduce((sum, obj) => sum + +obj?.balance, 0),
    [wallets]
  );

  const activeTotalBalance = useMemo(
    () => activeWallets.reduce((sum, obj) => sum + +obj?.balance, 0),
    [activeWallets]
  );

  return {
    walletAddresses,
    activeWallets,
    activeWalletAddresses,
    totalSuiBalance,
    activeTotalBalance,
  };
};
